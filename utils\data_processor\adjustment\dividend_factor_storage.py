"""
复权因子存储管理器

该模块负责复权因子数据的永久存储和管理，包括：
1. 从xtquant获取复权因子数据
2. 本地存储复权因子（使用parquet格式）
3. 增量更新复权因子数据
4. 提供复权因子查询接口

注意：复权因子是基础数据，应该永久存储，不是缓存数据
"""

import os
import pandas as pd
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import logging
from pathlib import Path

from utils.logger import get_unified_logger
from utils.smart_time_converter import smart_to_datetime

logger = get_unified_logger(__name__, enhanced=True)


class DividendFactorStorage:
    """复权因子存储管理器
    
    负责复权因子数据的永久存储和管理，包括获取、存储、查询和更新功能。
    复权因子作为基础数据，具有永久价值，需要长期保存。
    """
    
    def __init__(self, storage_dir: Optional[str] = None):
        """初始化复权因子存储管理器
        
        Args:
            storage_dir: 存储目录，默认为 data/dividend_factors
        """
        self.storage_dir = Path(storage_dir or "data/dividend_factors")
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        logger.debug(f"复权因子存储管理器初始化完成，存储目录: {self.storage_dir}")
    
    def get_storage_path(self, stock_code: str) -> Path:
        """获取股票复权因子的存储路径
        
        Args:
            stock_code: 股票代码
            
        Returns:
            存储路径
        """
        # 按股票代码分文件存储，便于管理和查询
        filename = f"{stock_code.replace('.', '_')}_dividend_factors.parquet"
        return self.storage_dir / filename
    
    def fetch_dividend_factors_from_xtquant(
        self, 
        stock_code: str, 
        start_time: str = '', 
        end_time: str = ''
    ) -> Optional[pd.DataFrame]:
        """从xtquant获取复权因子数据
        
        Args:
            stock_code: 股票代码
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            复权因子数据DataFrame，如果获取失败返回None
        """
        try:
            # 动态导入xtquant，避免在没有安装时报错
            import xtquant.xtdata as xtdata
            
            logger.debug(f"从xtquant获取复权因子: {stock_code}, 时间范围: {start_time} - {end_time}")
            
            # 调用xtquant API获取复权因子
            dividend_factors = xtdata.get_divid_factors(stock_code, start_time, end_time)
            
            if dividend_factors is None or dividend_factors.empty:
                logger.warning(f"未获取到股票 {stock_code} 的复权因子数据")
                return None
            
            # 添加获取时间戳，用于数据管理
            dividend_factors['fetch_time'] = datetime.now()
            dividend_factors['stock_code'] = stock_code
            
            logger.info(f"成功获取股票 {stock_code} 的复权因子数据，共 {len(dividend_factors)} 条记录")
            return dividend_factors
            
        except ImportError:
            logger.error("xtquant未安装，无法获取复权因子数据")
            return None
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 复权因子数据失败: {e}")
            return None
    
    def save_dividend_factors(self, stock_code: str, dividend_factors: pd.DataFrame) -> bool:
        """保存复权因子数据到本地存储
        
        Args:
            stock_code: 股票代码
            dividend_factors: 复权因子数据
            
        Returns:
            保存是否成功
        """
        try:
            storage_path = self.get_storage_path(stock_code)
            
            # 使用parquet格式存储，压缩率高，读取速度快
            dividend_factors.to_parquet(storage_path, compression='snappy')
            
            logger.info(f"成功保存股票 {stock_code} 的复权因子数据到: {storage_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存股票 {stock_code} 复权因子数据失败: {e}")
            return False
    
    def load_dividend_factors(self, stock_code: str) -> Optional[pd.DataFrame]:
        """从本地存储加载复权因子数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            复权因子数据DataFrame，如果不存在返回None
        """
        try:
            storage_path = self.get_storage_path(stock_code)
            
            if not storage_path.exists():
                logger.debug(f"股票 {stock_code} 的复权因子数据文件不存在: {storage_path}")
                return None
            
            dividend_factors = pd.read_parquet(storage_path)
            logger.debug(f"成功加载股票 {stock_code} 的复权因子数据，共 {len(dividend_factors)} 条记录")
            
            return dividend_factors
            
        except Exception as e:
            logger.error(f"加载股票 {stock_code} 复权因子数据失败: {e}")
            return None
    
    def update_dividend_factors(
        self, 
        stock_code: str, 
        force_update: bool = False,
        start_time: str = '',
        end_time: str = ''
    ) -> bool:
        """更新复权因子数据（增量更新）
        
        Args:
            stock_code: 股票代码
            force_update: 是否强制更新（重新获取所有数据）
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            更新是否成功
        """
        try:
            if force_update:
                # 强制更新：重新获取所有数据
                logger.info(f"强制更新股票 {stock_code} 的复权因子数据")
                new_factors = self.fetch_dividend_factors_from_xtquant(stock_code, start_time, end_time)
                
                if new_factors is not None:
                    return self.save_dividend_factors(stock_code, new_factors)
                return False
            
            # 增量更新：只获取新的复权因子数据
            existing_factors = self.load_dividend_factors(stock_code)
            
            if existing_factors is None:
                # 如果本地没有数据，获取全部数据
                logger.info(f"本地无股票 {stock_code} 复权因子数据，获取全部数据")
                new_factors = self.fetch_dividend_factors_from_xtquant(stock_code, start_time, end_time)
            else:
                # 如果有本地数据，从最后一条记录的时间开始获取
                if not existing_factors.empty and 'time' in existing_factors.columns:
                    last_time = existing_factors['time'].max()
                    # 从最后时间的下一天开始获取
                    next_day = (smart_to_datetime(last_time) + timedelta(days=1)).strftime('%Y%m%d')
                    logger.info(f"增量更新股票 {stock_code} 复权因子数据，从 {next_day} 开始")
                    new_factors = self.fetch_dividend_factors_from_xtquant(stock_code, next_day, end_time)
                else:
                    # 如果无法确定最后时间，重新获取全部数据
                    logger.warning(f"无法确定股票 {stock_code} 最后复权因子时间，重新获取全部数据")
                    new_factors = self.fetch_dividend_factors_from_xtquant(stock_code, start_time, end_time)
            
            if new_factors is None or new_factors.empty:
                logger.info(f"股票 {stock_code} 无新的复权因子数据")
                return True
            
            # 合并新旧数据
            if existing_factors is not None and not existing_factors.empty:
                # 去重合并（基于时间字段）
                if 'time' in existing_factors.columns and 'time' in new_factors.columns:
                    combined_factors = pd.concat([existing_factors, new_factors], ignore_index=True)
                    combined_factors = combined_factors.drop_duplicates(subset=['time'], keep='last')
                    combined_factors = combined_factors.sort_values('time').reset_index(drop=True)
                else:
                    combined_factors = new_factors
            else:
                combined_factors = new_factors
            
            return self.save_dividend_factors(stock_code, combined_factors)
            
        except Exception as e:
            logger.error(f"更新股票 {stock_code} 复权因子数据失败: {e}")
            return False
    
    def query_dividend_factors(
        self, 
        stock_code: str, 
        start_date: Optional[str] = None, 
        end_date: Optional[str] = None
    ) -> Optional[pd.DataFrame]:
        """查询复权因子数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            符合条件的复权因子数据
        """
        try:
            dividend_factors = self.load_dividend_factors(stock_code)
            
            if dividend_factors is None or dividend_factors.empty:
                return None
            
            # 如果指定了日期范围，进行过滤
            if start_date or end_date:
                if 'time' in dividend_factors.columns:
                    # 使用smart_to_datetime进行时间转换，避免时区问题
                    time_col = smart_to_datetime(dividend_factors['time'])

                    if start_date:
                        start_dt = smart_to_datetime(start_date)
                        # 修复布尔索引警告：确保索引对齐
                        mask = time_col >= start_dt
                        dividend_factors = dividend_factors.loc[mask]
                        # 重新获取过滤后的时间列，避免索引长度不匹配
                        time_col = smart_to_datetime(dividend_factors['time'])

                    if end_date:
                        end_dt = smart_to_datetime(end_date)
                        # 修复布尔索引警告：确保索引对齐
                        mask = time_col <= end_dt
                        dividend_factors = dividend_factors.loc[mask]
            
            logger.debug(f"查询股票 {stock_code} 复权因子数据，返回 {len(dividend_factors)} 条记录")
            return dividend_factors
            
        except Exception as e:
            logger.error(f"查询股票 {stock_code} 复权因子数据失败: {e}")
            return None
    
    def get_storage_info(self) -> Dict[str, Any]:
        """获取存储信息统计
        
        Returns:
            存储信息字典
        """
        try:
            info = {
                'storage_dir': str(self.storage_dir),
                'total_files': 0,
                'total_size_mb': 0,
                'stock_list': []
            }
            
            if self.storage_dir.exists():
                parquet_files = list(self.storage_dir.glob("*.parquet"))
                info['total_files'] = len(parquet_files)
                
                total_size = 0
                for file_path in parquet_files:
                    total_size += file_path.stat().st_size
                    # 从文件名提取股票代码
                    stock_code = file_path.stem.replace('_dividend_factors', '').replace('_', '.')
                    info['stock_list'].append(stock_code)
                
                info['total_size_mb'] = round(total_size / (1024 * 1024), 2)
            
            return info
            
        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {'error': str(e)}


# 创建全局实例
dividend_factor_storage = DividendFactorStorage()
